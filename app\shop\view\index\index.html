<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$view_env_name}{$config.name}</title>
    <link rel="shortcut icon" href="{$storageUrl}{$config.web_favicon}"/>
    <link rel="stylesheet" href="__PUBLIC__/static/lib/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/static/admin/css/app.css">
    <script src="__PUBLIC__/static/lib/layui/layui.js"></script>
    <script src="__PUBLIC__/static/admin/js/app.js"></script>
    <style>
        /* Global Styles */
        body {
            background-color: #f4f6f8 !important;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        }
        .layui-card {
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.06);
            border: 1px solid #e6e6e6;
        }
        .layui-card-header {
            font-weight: 600;
            font-size: 16px;
            color: #333;
            background-color: #fff;
            border-bottom: 1px solid #f0f0f0;
        }
        .layui-header {
            box-shadow: 0 2px 4px rgba(0,0,0,.08);
        }
        .layui-side-menu .layui-nav-item a:hover {
            background-color: #f6f6f6;
        }
        .layui-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 60px;
        }
        .layui-logo img {
            max-height: 40px;
            max-width: 80%;
        }

        /* 商家信息显示样式 */
        .shop-info-display {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .shop-logo {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            flex-shrink: 0;
        }

        .shop-info-display a {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .shop-info-display cite {
            font-style: normal;
            font-weight: 500;
            color: #fff;
        }

        /* 侧边栏商家信息样式 */
        .shop-info-sidebar {
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 10px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .shop-logo-container {
            flex-shrink: 0;
        }

        .shop-logo-sidebar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid rgba(255,255,255,0.3);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .shop-name-container {
            flex: 1;
            min-width: 0;
        }

        .shop-name {
            color: #fff;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .shop-id {
            color: rgba(255,255,255,0.8);
            font-size: 12px;
            font-weight: 400;
        }

        /* 当没有logo时的样式调整 */
        .shop-info-sidebar:not(:has(.shop-logo-container)) {
            justify-content: center;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- 头部区域 -->
    <div class="layui-header">
        <ul class="layui-nav layui-layout-left">
            <li class="layui-nav-item">
                <a class="refresh" href="javascript:" title="刷新">
                   <i class="layui-icon layui-icon-refresh-3"></i>
                </a>
            </li>
        </ul>
        <ul class="layui-nav layui-layout-right">
            <li class="layui-nav-item layui-hide-xs">
                <a class="fullscreen" href="javascript:">
                    <i class="layui-icon layui-icon-screen-full"></i>
                </a>
            </li>
            <!-- 商家信息显示 -->
            <li class="layui-nav-item shop-info-display">
                {notempty name="shop_info.logo"}
                <img src="{$storageUrl}{$shop_info.logo}" class="shop-logo" alt="{$shop_info.name}">
                {/notempty}
                <a href="javascript:">
                    <cite>{$admin_name}（{$shop_info.name}）</cite>
                <span class="layui-nav-more"></span></a>
                <dl class="layui-nav-child layui-anim layui-anim-upbit userinfo">
<!--                    <dd><a lay-id="u-1" href="javascript:" data-url="pages/member/user.html">个人中心<span class="layui-badge-dot"></span></a></dd>-->
<!--                    <dd><hr></dd>-->
                    <dd><a href="{:url('login/logout')}" id="logout">退出登录</a></dd>
                </dl>
            </li>
        </ul>
    </div>

    <!-- 菜单区域 -->
    <div class="layui-sidebar ">
        <!-- <div class="layui-logo">
            <img  src="{$storageUrl}{$config.backstage_logo}">
        </div> -->

        <!-- 商家信息展示区域 -->
        <div class="shop-info-sidebar">
            {notempty name="shop_info.logo"}
            <div class="shop-logo-container">
                <img src="{$shop_info.logo}" class="shop-logo-sidebar" alt="{$shop_info.name}">
            </div>
            {/notempty}
            <div class="shop-name-container">
                <div class="shop-name">{$shop_info.name}</div>
                <div class="shop-id">ID: {$shop_info.id}</div>
            </div>
        </div>
        <ul class="layui-side-menu">
            {volist name="menu" id="vo"}
                <li>
                    {empty name="vo.sub"}
                        <a lay-id="{$vo.id}" lay-href="{:url($vo.uri)}" class="active">
                            <i class="layui-icon {$vo.icon}"></i>
                            <cite>{$vo.name}</cite>
                        </a>
                    {else/}
                        <a href="javascript:">
                            <i class="layui-icon {$vo.icon}"></i>
                            <cite>{$vo.name}</cite>
                        </a>

                        <dl class="child-menu">
                            <dt><strong>{$vo.name}</strong></dt>
                            {volist name="vo.sub" id="second"}
                                <dd>
                                    {empty name="second.sub"}
                                        <a lay-id="{$second.id}" lay-href="{:url($second.uri)}">{$second.name}</a>
                                    {else /}
                                        <a class="child-menu-title">
                                            <i class="layui-icon layui-icon-triangle-d"></i>
                                            <cite>{$second.name}</cite>
                                        </a>
                                        <dl>
                                            {volist name="second.sub" id="third"}
                                            <dd><a lay-id="{$third.id}" lay-href="{:url($third.uri)}">{$third.name}</a></dd>
                                            {/volist}
                                        </dl>
                                    {/empty}
                                </dd>
                            {/volist}
                        </dl>
                    {/empty}
                </li>
            {/volist}
        </ul>
    </div>

    <!-- 标签区域 -->
    <div class="lay-pagetabs" id="LAY_app_tabs">
        <div class="layui-icon lay-tabs-control layui-icon-prev" lay-event="leftPage"></div>
        <div class="layui-icon lay-tabs-control layui-icon-next" lay-event="rightPage"></div>
        <div class="layui-icon lay-tabs-control layui-icon-down">
            <ul class="layui-nav lay-tabs-select" lay-filter="lay-pagetabs-nav">
                <li class="layui-nav-item" lay-unselect>
                    <a href="javascript:"><span class="layui-nav-more"></span></a>
                    <dl class="layui-nav-child layui-anim-fadein11 ">
                        <dd lay-event="closeThisTabs"><a href="javascript:">关闭当前标签页</a></dd>
                        <dd lay-event="closeOtherTabs"><a href="javascript:">关闭其它标签页</a></dd>
                        <dd lay-event="closeAllTabs"><a href="javascript:">关闭全部标签页</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
        <div class="layui-tab" lay-unauto lay-allowclose="true" lay-filter="lay-layout-tabs">
            <ul class="layui-tab-title" id="LAY_app_tabsheader">
                <li lay-id="0" lay-attr="{:url('index/stat')}" class="layui-this">工作台<i class="layui-icon layui-tab-close">ဆ</i></li>
            </ul>
        </div>
    </div>

    <!-- 主体区域 -->
    <div class="layui-body" id="LAY_app_body">
        <div lay-id="0" class="lay-tabsbody-item layui-show">
            <iframe src="{:url('index/stat')}" class="lay-iframe"></iframe>
        </div>
    </div>

</body>
</html>
