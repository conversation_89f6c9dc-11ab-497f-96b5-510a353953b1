<?php
// +----------------------------------------------------------------------
// | likeshop100%开源免费商用商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | 商业版本务必购买商业授权，以免引起法律纠纷
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshopTeam
// +----------------------------------------------------------------------
namespace app\shopapi\logic;
use app\admin\controller\activity_area\Area;
use app\common\enum\NoticeEnum;
use app\common\enum\WithdrawalEnum;
use app\common\model\shop\Shop;
use app\common\model\goods\Goods;
use app\common\model\shop\ShopAccountLog;
use app\common\model\shop\ShopAdmin;
use app\common\model\shop\ShopBank;
use app\common\model\shop\ShopCategory;
use app\common\model\shop\ShopWithdrawal;
use app\common\server\AreaServer;
use app\common\server\ConfigServer;
use think\facade\Db;
use think\facade\Log;
use app\common\model\shop\ShopDeposit;
use app\common\model\shop\ShopDepositDetails;
use app\common\model\shop\ShopMerchantfees;
use app\common\model\order\OrderTrade;
use app\api\logic\PayLogic;
use app\common\enum\PayEnum;
use app\common\enum\OrderEnum;
use app\common\enum\ClientEnum;
use app\common\enum\AfterSaleEnum;
use app\common\server\UrlServer;


/**
 * 商家逻辑层
 * Class ShopLogic
 * @package app\shopapi\logic
 */
class ShopLogic{

    private $error;

    /**
     * @notes 获取错误信息
     * @return mixed
     */
    public function getError()
    {
        return $this->error;
    }

    /**
     * @notes 获取商家可提现余额
     * @param $shop_id
     * @return mixed
     * <AUTHOR>
     * @date 2021/11/10 16:15
     */
    public function getShopInfo(int $shop_id){
        $shop = Shop::where(['id'=>$shop_id])
            ->field("id,cid,shop_label,shop_doc,tier_level,videos,yan_level,name,business_license,other_qualifications,logo,is_run,wallet,score,nickname,mobile,intro,
            run_start_time,yan_fee,run_end_time,weekdays,province_id,city_id,district_id,address,refund_address,open_invoice,spec_invoice")
            ->find()->toArray();

        $shop['run_start_time'] = date('H:i',$shop['run_start_time']);
        $shop['run_end_time'] = date('H:i',$shop['run_end_time']);

        $shop['shop_doc']= $shop['shop_doc']? $shop['shop_doc']:Db::name('shop_apply')->where('name',$shop['name'])->value('shop_doc');
        $shop['province_name'] = '';
        $shop['city_name'] = '';
        $shop['district_name'] = '';
        $label=ConfigServer::get('website_shop', 'merchant_tags');
        if($label){
            $shop['all_label']=explode('|', $label);    
        }else{
            $shop['all_label']=[];
        }
        $shop['shop_label']=$shop['shop_label']?:'';
        $shop['province_id'] && $shop['province_name'] = AreaServer::getAddress($shop['province_id']);
        $shop['city_id'] && $shop['city_name'] = AreaServer::getAddress($shop['city_id']);
        $shop['district_id'] && $shop['district_name'] = AreaServer::getAddress($shop['district_id']);
        // $shop['shop_label']=$shop['shop_label']?:implode(',',$shop['shop_label']);
        $shop_deposit=Db::name('shop_deposit')->where('shop_id',$shop['id'])->where('pay_status',1)->value('id');
        $shop['shop_type']=$shop_deposit?'集采联盟商家':'普通商家';
        $shop['refund_address']['province_name'] = !empty($shop['refund_address']['province_id']) ? AreaServer::getAddress($shop['refund_address']['province_id']) : '';
        $shop['refund_address']['city_name'] = !empty($shop['refund_address']['city_id']) ? AreaServer::getAddress($shop['refund_address']['city_id']) : '';
        $shop['refund_address']['district_name'] = !empty($shop['refund_address']['district_id']) ? AreaServer::getAddress($shop['refund_address']['district_id']) : '';
        $shop['level_name']=$shop['yan_level']?Db::name('shop_level')->where('id',$shop['yan_level'])->value('name'):'';
        $shop['cate_name'] = ShopCategory::where('id', $shop['cid'])->value('name');
        $shop['banner_img']=UrlServer::getFileUrl(Db::name('ad')->where('id',160)->value('image'));   


        return $shop;

    }

    /**
     * @notes 获取提现信息
     * @param int $shop_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/11/10 16:30
     */
    public function getWithdrawInfo(int $shop_id){
        $wallet = Shop::where(['id'=>$shop_id])
            ->value("wallet");

        $min_withdrawal_money = ConfigServer::get('shop_withdrawal', 'min_withdrawal_money', 0);
        $max_withdrawal_money = ConfigServer::get('shop_withdrawal', 'max_withdrawal_money', 0);
        $withdrawal_service_charge = ConfigServer::get('shop_withdrawal', 'withdrawal_service_charge', 0);

        $bank_list = ShopBank::where(['shop_id'=>$shop_id,'del'=>0])
                        ->field('id,name,branch,nickname,account')
                        ->select()->toArray();
        return [
            'wallet'                    => $wallet,
            'min_withdrawal_money'      => $min_withdrawal_money,
            'max_withdrawal_money'      => $max_withdrawal_money,
            'withdrawal_service_charge' => $withdrawal_service_charge,
            'bank_list'                 => $bank_list,
        ];

    }

    /**
     * @notes 提现金额
     * @param array $post
     * @return bool|string
     * <AUTHOR>
     * @date 2021/11/10 16:56
     */
    public function withdraw(array $post){
        Db::startTrans();
        try {
            $shop_id = $post['shop_id'];
            // 1、获取提现条件
            $withdrawal_service_charge = ConfigServer::get('shop_withdrawal', 'withdrawal_service_charge', 0);

            // 2、获取商家信息
            $shop   = (new Shop())->findOrEmpty($shop_id)->toArray();

            // 4、获取商家提现手续费
            $poundage_amount   = 0;
            if ($withdrawal_service_charge > 0) {
                $proportion = $withdrawal_service_charge / 100;
                $poundage_amount = $post['money'] * $proportion;
                $poundage_amount = $poundage_amount <= 0 ? 0 : $poundage_amount;
            }

            // 5、创建申请记录
            $withdrawal = ShopWithdrawal::create([
                'sn'              => createSn('shop_withdrawal', 'sn'),
                'bank_id'         => $post['bank_id'],
                'shop_id'         => $shop_id,
                'apply_amount'    => floatval($post['money']),
                'left_amount'     => $post['money'] - $poundage_amount,
                'poundage_amount' => $poundage_amount,
                'poundage_ratio'  => $withdrawal_service_charge,
                'status'          => WithdrawalEnum::APPLY_STATUS
            ]);
            // 6、扣除商家可提现金额
            Shop::update([
                'wallet'      => ['dec', floatval($post['money'])],
                'update_time' => time()
            ], ['id' => $shop_id]);

            $left_amount =  Shop::where(['id' => $shop_id])->value('wallet');
            // 7、增加提现流水记录(待提现)
            $logType = ShopAccountLog::withdrawal_stay_money;
            ShopAccountLog::decData($shop_id, $logType, $post['money'], $left_amount, [
                'source_id' => $withdrawal['id'],
                'source_sn' => $withdrawal['sn'],
                'remark'    => '商家提现'
            ]);

            $platform_contacts = ConfigServer::get('website_platform', 'platform_mobile');
            if (!empty($platform_contacts)) {
                event('Notice', [
                    'scene' => NoticeEnum::SHOP_WITHDRAWAL_NOTICE_PLATFORM,
                    'mobile' => $platform_contacts,
                    'params' => [
                        'shop_withdrawal_sn' => $withdrawal['sn'],
                        'shop_name' => $shop['name'],
                    ]
                ]);
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            return $e->getMessage();
        }
    }

    /**
     * @notes 提现记录
     * @param $shop_id
     * @param $page_no
     * @param $page_size
     * @return array
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2021/11/10 17:10
     */
    public function withdrawLog(int $shop_id,int $page_no,int $page_size){
        $lists = ShopWithdrawal::alias('SW')
            ->join('shop_account_log SCL','SW.sn = SCL.source_sn')
            ->where(['SW.shop_id'=>$shop_id,'source_type'=>[ShopAccountLog::withdrawal_stay_money,ShopAccountLog::withdrawal_dec_money,ShopAccountLog::withdrawal_fail_money]])
            ->field("SCL.id,SCL.change_amount,SCL.left_amount,status,SCL.create_time")
            ->paginate([
                'page'      => $page_no,
                'list_rows' => $page_size,
                'var_page' => 'page'
            ])->toArray();

        return ['count' => $lists['total'], 'lists' => $lists['data']];
    }


    /**
     * @notes 添加银行账户
     * @param $post
     * @return bool
     * <AUTHOR>
     * @date 2021/11/10 18:30
     */
    public function addBank(array $post){
        $shop_bank = new ShopBank();
        $shop_bank->shop_id     = $post['shop_id'];
        $shop_bank->name        = $post['name'];
        $shop_bank->branch      = $post['branch'];
        $shop_bank->nickname    = $post['nickname'];
        $shop_bank->account     = $post['account'];
        $shop_bank->save();
        return true;
    }

    /**
     * @notes 获取银行卡
     * @param int $id
     * @param int $shop_id
     * @return array
     * <AUTHOR>
     * @date 2021/11/11 15:46
     */
    public function getBank(int $id,int $shop_id){
        $shop_bank = ShopBank::where(['id'=>$id,'shop_id'=>$shop_id])
                    ->field('id,name,branch,nickname,account')
                    ->findOrEmpty()->toArray();
        return $shop_bank;
    }

    /**
     * @notes 编辑银行账户
     * @param $post
     * @return bool
     * <AUTHOR>
     * @date 2021/11/10 18:38
     */
    public function editBank(array $post){
        ShopBank::update([
            'name'     => $post['name'],
            'branch'   => $post['branch'],
            'nickname' => $post['nickname'],
            'account'  => $post['account'],
            'del'      => 0
        ], ['id'=>$post['id']]);
        return true;

    }

    /**
     * @notes 删除银行卡
     * @param $id
     * @param $shop_id
     * @return bool
     * <AUTHOR>
     * @date 2021/11/10 18:42
     */
    public function delBank(int $id,int $shop_id){
        ShopBank::where(['id'=>$id,'shop_id'=>$shop_id])->delete();
        return true;
    }

    /**
     * @notes 更新商家信息
     * @param array $post
     * @param int $shop_id
     * @return Shop
     * <AUTHOR>
     * @date 2021/11/11 11:34
     */
    public function shopSet(array $post,int $shop_id){
        if(isset($post['refund_address'])){
            // 确保 refund_address 存储为 JSON 字符串
            if (is_array($post['refund_address'])) {
                 // 检查地址数组内部字段是否都填写
                 $refundAddress = $post['refund_address'];
                 if (empty($refundAddress['name']) || empty($refundAddress['mobile']) || empty($refundAddress['province_id']) || empty($refundAddress['city_id']) || empty($refundAddress['district_id']) || empty($refundAddress['address'])) {
                     // 如果退货地址信息不完整，可以考虑不更新或返回错误，这里暂时按原逻辑处理
                 }

                 $post['refund_address'] = json_encode($refundAddress, JSON_UNESCAPED_UNICODE);
            } else {
                 // 如果传入的不是数组，可能需要处理或记录错误，这里假设传入的是有效JSON或空
                 // 如果需要强制必须是数组格式，可以在此添加验证逻辑
            }
        }
        if(!isset($post['other_qualifications'])){
            $post['other_qualifications'] = '';
        }
       
        // 处理营业时间，将其从 H:i 格式转为时间戳存储
       
        // if (isset($post['run_start_time'])) {
        //     $post['run_start_time'] = strtotime($post['run_start_time']);
        // }
        // if (isset($post['run_end_time'])) {
        //     $post['run_end_time'] = strtotime($post['run_end_time']);
        // }
        return Shop::update($post,['id'=>$shop_id]);
    }

    /**
     * @notes 检查商家资料完善程度
     * @param int $shop_id
     * @return array 返回未完善字段的提示信息数组
     * <AUTHOR> AI
     * @date 2024/05/16
     */
    public function checkProfileCompletion(int $shop_id): array
    {
        $shop = Shop::find($shop_id);
        if (!$shop) {
            return ['店铺信息不存在']; // 或者抛出异常
        }

        $incompleteFields = [];
        $fieldMap = [
            'cid' => '店铺分类',
            'name' => '店铺名称',
            'logo' => '店铺logo',
            'intro' => '店铺简介',
            'nickname' => '联系人姓名',
            'mobile' => '联系电话',
            'province_id' => '省份',
            'city_id' => '城市',
            'district_id' => '区县',
            'address' => '商家详细地址',
            // 'run_start_time' => '营业开始时间',
            // 'run_end_time' => '营业结束时间',
            // 'weekdays' => '营业星期',
            'business_license' => '营业执照',
            // 'other_qualifications' => '其他资质',
            'refund_address' => '退货地址',
        ];

        // 检查基本字段是否为空或默认值
        foreach ($fieldMap as $field => $label) {
            switch ($field) {
                // case 'cid':
                // case 'province_id':
                // case 'city_id':
                // case 'district_id':
                // case 'run_start_time': // 时间戳存储，检查是否为0
                case 'refund_address':
                    $refundAddressValue = $shop->$field;
                    $refundAddressData = null;

                    // 检查 refund_address 的类型
                    if (is_string($refundAddressValue) && !empty($refundAddressValue)) {
                        // 如果是字符串，尝试解码
                        $decoded = json_decode($refundAddressValue, true);
                        // 检查解码是否成功且结果是数组
                        if (is_array($decoded)) {
                            $refundAddressData = $decoded;
                        } else {
                             // 如果解码失败或结果不是数组，视为空或无效
                             $refundAddressValue = ''; // 将其视为空字符串以便后续检查
                        }
                    } elseif (is_array($refundAddressValue)) {
                        // 如果已经是数组，直接使用
                        $refundAddressData = $refundAddressValue;
                    } else {
                         // 其他类型或空值，视为空
                         $refundAddressValue = '';
                    }


                    // 检查退货地址是否为空或无效
                    if (empty($refundAddressValue) && $refundAddressData === null) {
                         $incompleteFields[] = $label . ',请完善';
                    } elseif ($refundAddressData !== null) {
                         // 进一步检查退货地址内部字段是否完整
                         if (empty($refundAddressData['nickname']) || empty($refundAddressData['mobile']) || empty($refundAddressData['province_id']) || empty($refundAddressData['city_id']) || empty($refundAddressData['district_id']) || empty($refundAddressData['address'])) {

                             $incompleteFields[] = $label . ',信息不完整,请完善';
                         }
                    } else {
                         // 如果 $refundAddressValue 非空但 $refundAddressData 为 null (例如无效JSON字符串)，也标记为未填写或信息不完整
                         $incompleteFields[] = $label . '.请完善';
                    }
                    break;
                default: // 适用于 name, logo, intro, nickname, mobile, address, weekdays 等字符串字段
                    if (empty($shop->$field) || $shop->$field == '') {
                        $incompleteFields[] = $label . ',请完善';
                    }
                    break;
            }
        }

         // 对地址进行合并检查，如果省市区任一未填，只提示一次
         $addressIncomplete = false;
         foreach (['province_id', 'city_id', 'district_id', 'address'] as $addrField) {
             if (in_array($fieldMap[$addrField] . '未填写', $incompleteFields)) {
                 $addressIncomplete = true;
                 // 从数组中移除单独的省市区和详细地址提示
                 $incompleteFields = array_filter($incompleteFields, function($value) use ($fieldMap, $addrField) {
                     return $value !== $fieldMap[$addrField] . '请完善';
                 });
             }
         }
         if ($addressIncomplete) {
             $incompleteFields[] = '店铺地址信息不完整';
         }

         // 对营业时间进行合并检查
        //  $runTimeIncomplete = false;
        //  foreach (['run_start_time', 'run_end_time', 'weekdays'] as $timeField) {
        //       if (in_array($fieldMap[$timeField] . '未填写', $incompleteFields)) {
        //           $runTimeIncomplete = true;
        //           $incompleteFields = array_filter($incompleteFields, function($value) use ($fieldMap, $timeField) {
        //               return $value !== $fieldMap[$timeField] . '未填写';
        //           });
        //       }
        //  }
        //  if ($runTimeIncomplete) {
        //       $incompleteFields[] = '营业时间信息不完整';
        //  }

         //判断是否有商品
         $goodsCount = Goods::where('shop_id', $shop_id)->where('status',1)->where('audit_status',1)->count();
         if ($goodsCount < 1) {
             $incompleteFields[] = '请上架不少于两个商品';
         }
         


        return array_values(array_unique($incompleteFields)); // 去重并重置索引
    }


    /**
     * @notes 修改密码
     * @param $password
     * @param $admin_id
     * @param $shop_id
     * @return bool
     * <AUTHOR>
     * @date 2021/11/11 16:03
     */
    public function updatePassword(array $post,int $shop_id)
    {
        try {
            $admin = ShopAdmin::where(['id' => $post['admin_id'], 'shop_id' => $shop_id])->find();
            $admin->password = generatePassword($post['password'], $admin['salt']);
            $admin->save();
            return true;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    /**
     * @notes 获取保证金明细
     * @param int $shopId
     * @param int $pageNo
     * @param int $pageSize
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function getDepositDetails(int $shopId, int $pageNo = 1, int $pageSize = 15): array
    {
        $lists = ShopDepositDetails::where(['shop_id' => $shopId])
            ->order('id', 'desc')
            ->paginate([
                'list_rows' => $pageSize,
                'page' => $pageNo,
            ])->toArray();

        return ['count' => $lists['total'], 'lists' => $lists['data'],'more'=>is_more($lists['total'], $pageNo, $pageSize)];
    }

    /**
     * @notes 创建保证金补缴订单并调起支付
     * @param array $data ['shop_id', 'amount', 'pay_way']
     * @return false|array Payment parameters or false on failure
     */
    public function createDepositRechargeOrder(array $data)
    {
        $shopId = $data['shop_id'];
        $amount = floatval($data['amount']);
        $payWay = intval($data['pay_way']);

        if ($amount <= 0) {
            $this->error = '补缴金额必须大于0';
            return false;
        }
        if (!in_array($payWay, PayEnum::PAY_WAY_LIST)) {
             $this->error = '无效的支付方式';
             return false;
        }

        // 获取商家信息
        $shop = Shop::find($shopId);
        if (!$shop) {
            $this->error = '商家不存在';
            return false;
        }

        Db::startTrans();
        try {
            // 1. 创建商家费用订单 (用于支付流程)
            $orderSn = createSn(ShopMerchantfees::SN_PREFIX, 'shop_merchantfees');
            $merchantFeeData = [
                'shop_id' => $shopId,
                'order_sn' => $orderSn,
                'type' => ShopMerchantfees::TYPE_DEPOSIT_RECHARGE,
                'amount' => $amount,
                'pay_way' => $payWay,
                'status' => ShopMerchantfees::STATUS_UNPAID,
                'create_time' => time(),
            ];
            $merchantFee = ShopMerchantfees::create($merchantFeeData);

            // 2. 创建交易记录 (用于统一支付回调)
            $tradeData = [
                'order_sn' => $orderSn,
                'order_id' => $merchantFee->id,
                'order_type' => OrderEnum::SHOP_DEPOSIT_RECHARGE,
                'trade_no' => '',
                'pay_status' => OrderTrade::PAY_STATUS_UNPAID,
                'transaction_id' => '',
                'total_amount' => $amount,
                'create_time' => time(),
                'pay_way' => $payWay,
            ];
            OrderTrade::create($tradeData);

            // 3. 调用支付逻辑 (暂时注释掉，需根据 PayLogic 实现调整)
            /*
            $payLogic = new PayLogic();
            $payParams = $payLogic->placeOrder($orderSn, PayEnum::SHOP_DEPOSIT_RECHARGE, $payWay, ClientEnum::SHOP_API, $shopId);

            if ($payParams === false) {
                $this->error = $payLogic->getError() ?: '发起支付失败';
                Db::rollback();
                return false;
            }
            */

            Db::commit();
            // Temporarily return the order SN, replace with $payParams when payment logic is uncommented
            // return $payParams;
            return ['order_sn' => $orderSn, 'message' => '订单创建成功，支付部分待实现'];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('创建保证金补缴订单失败: '. $e->getMessage());
            $this->error = '创建补缴订单失败: ' . $e->getMessage();
            return false;
        }
    }

    /**
     * @notes 修改手机号
     * @param int $shop_id 店铺ID
     * @param int $admin_id 管理员ID
     * @param array $post 请求数据
     * @return bool
     */
    public function updateMobile($shop_id, $admin_id, $post)
    {
        Db::startTrans();
        try {
            // 验证原手机号是否正确
            $admin = ShopAdmin::where([
                ['id', '=', $admin_id],
                ['shop_id', '=', $shop_id],
                ['mobile', '=', $post['old_mobile']]
            ])->find();

            if (!$admin) {
                throw new \Exception('原手机号验证失败');
            }

            // 验证密码
            if (!password_verify($post['password'], $admin['password'])) {
                throw new \Exception('密码验证失败');
            }

            // 验证验证码
            $check = (new \app\common\server\SmsServer())->checkCode($post['new_mobile'], $post['verify_code'], 'change_mobile');
            if ($check === false) {
                throw new \Exception('验证码错误');
            }

            // 检查新手机号是否已被使用
            $exists = ShopAdmin::where([
                ['mobile', '=', $post['new_mobile']],
                ['id', '<>', $admin_id]
            ])->find();

            if ($exists) {
                throw new \Exception('该手机号已被使用');
            }

            // 更新手机号
            $admin->mobile = $post['new_mobile'];
            $admin->update_time = time();
            $admin->save();

            // 记录修改日志
            $this->recordOperationLog($shop_id, $admin_id, '修改手机号', [
                'old_mobile' => $post['old_mobile'],
                'new_mobile' => $post['new_mobile']
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @notes 检查商家是否满足注销条件
     * @param int $shop_id 商家ID
     * @return array 返回检查结果数组，包含每个条件的状态
     */
    public function checkDeactivateConditions(int $shop_id)
    {
        $result = [
            'can_deactivate' => true,
            'conditions' => [
                'orders_completed' => true, // 所有订单已完成或已关闭
                'aftersale_completed' => true, // 所有售后已处理完成
                'settlement_completed' => true, // 没有未结算资金
                'withdrawal_completed' => true, // 没有未提现资金
            ],
            'messages' => []
        ];

        // 1. 检查订单完结状态
        $unfinishedOrders = Db::name('order')
            ->where('shop_id', $shop_id)
            ->where('order_status', 'not in', [OrderEnum::ORDER_STATUS_COMPLETE, OrderEnum::ORDER_STATUS_DOWN])
            ->where('del', 0)
            ->count();

        if ($unfinishedOrders > 0) {
            $result['conditions']['orders_completed'] = false;
            $result['can_deactivate'] = false;
            $result['messages'][] = "您有 {$unfinishedOrders} 个订单未完成，请先处理完成";
        }

        // 2. 检查售后处理状态
        $pendingAftersales = Db::name('after_sale')
            ->alias('a')
            ->join('order o', 'a.order_id = o.id')
            ->where('o.shop_id', $shop_id)
            ->where('a.status', 'not in', [AfterSaleEnum::STATUS_COMPLETE, AfterSaleEnum::STATUS_MECHANT_REFUSED])
            ->where('a.del', 0)
            ->count();

        if ($pendingAftersales > 0) {
            $result['conditions']['aftersale_completed'] = false;
            $result['can_deactivate'] = false;
            $result['messages'][] = "您有 {$pendingAftersales} 个售后订单未处理完成，请先处理完成";
        }

        // 3. 检查未结算资金
        $unsettledFunds = Db::name('distribution_order_goods')
            ->where('shop_id', $shop_id)
            ->where('status', OrderEnum::SETTLE_WAIT) // 待结算状态
            ->sum('money');

        if ($unsettledFunds > 0) {
            $result['conditions']['settlement_completed'] = false;
            $result['can_deactivate'] = false;
            $result['messages'][] = "您有 {$unsettledFunds} 元未结算资金，请等待系统结算";
        }

        // 4. 检查未提现资金
        $wallet = Shop::where('id', $shop_id)->value('wallet');

        if ($wallet > 0) {
            $result['conditions']['withdrawal_completed'] = false;
            $result['can_deactivate'] = false;
            $result['messages'][] = "您有 {$wallet} 元未提现资金，请先提现";
        }

        return $result;
    }

    /**
     * @notes 申请注销账号
     * @param int $shop_id
     * @param int $admin_id
     * @param array $post
     * @return array|string 成功返回检查结果数组，失败返回错误信息
     */
    public function applyDeactivate($shop_id, $admin_id, $post)
    {
        Db::startTrans();
        try {
            // 验证密码
            $admin = ShopAdmin::where([
                ['root', '=', 1],
                ['shop_id', '=', $shop_id]
            ])->find();
            $password = generatePassword($post['password'], $admin['salt']);//生成密码

            if (!$admin || $password!=$admin['password']) {
                throw new \Exception('密码验证失败');
            }

            // 检查注销条件
            $checkResult = $this->checkDeactivateConditions($shop_id);

            // 创建注销申请记录
            $deactivateData = [
                'shop_id' => $shop_id,
                'admin_id' => $admin_id,
                'reason' => $post['reason'] ?? '自愿注销',
                'status' => 0, // 待审核
                'create_time' => time(),
                'ip' => request()->ip(),
                'check_result' => json_encode($checkResult, JSON_UNESCAPED_UNICODE)
            ];

            Db::name('shop_deactivate_apply')->insert($deactivateData);

            // 记录操作日志
            $this->recordOperationLog($shop_id, $admin_id, '申请注销账号', [
                'reason' => $post['reason'] ?? '自愿注销',
                'check_result' => $checkResult
            ]);

            Db::commit();
            return $checkResult;
        } catch (\Exception $e) {
            Db::rollback();
            return $e->getMessage();
        }
    }

    /**
     * @notes 获取账号安全信息
     * @param int $shop_id
     * @param int $admin_id
     * @return array
     */
    public function getAccountSecurity($shop_id, $admin_id)
    {
        $admin = ShopAdmin::where([
            ['id', '=', $admin_id],
            ['shop_id', '=', $shop_id]
        ])->find();

        if (!$admin) {
            return [];
        }

        return [
            'mobile' => $admin['mobile'],
            'mobile_bind_time' => $admin['mobile_bind_time'],
            'last_login_time' => $admin['last_login_time'],
            'last_login_ip' => $admin['last_login_ip'],
            'security_level' => $this->calculateSecurityLevel($admin),
            'two_factor_auth' => !empty($admin['google_secret']),
            'login_verify' => !empty($admin['login_verify']),
        ];
    }

    /**
     * @notes 计算账号安全等级
     * @param object $admin
     * @return int
     */
    private function calculateSecurityLevel($admin)
    {
        $level = 0;

        // 基础信息完整性
        if (!empty($admin['mobile'])) $level += 1;
        if (!empty($admin['email'])) $level += 1;

        // 安全设置
        if (!empty($admin['google_secret'])) $level += 2;
        if (!empty($admin['login_verify'])) $level += 1;

        // 密码强度
        if (strlen($admin['password']) >= 8) $level += 1;

        return min($level, 5); // 最高5级
    }

    /**
     * @notes 记录操作日志
     * @param int $shop_id
     * @param int $admin_id
     * @param string $action
     * @param array $data
     */
    private function recordOperationLog($shop_id, $admin_id, $action, $data = [])
    {
        $log = [
            'shop_id' => $shop_id,
            'admin_id' => $admin_id,
            'action' => $action,
            'ip' => request()->ip(),
            'create_time' => time(),
            'data' => json_encode($data, JSON_UNESCAPED_UNICODE)
        ];

        Db::name('shop_operation_log')->insert($log);
    }

    /**
     * @notes 执行商家注销流程（由管理员审核通过后调用）
     * @param int $shop_id 商家ID
     * @return array 返回执行结果
     */
    public function executeDeactivate(int $shop_id)
    {
        $result = [
            'success' => true,
            'messages' => []
        ];

        Db::startTrans();
        try {
            // 1. 下架该店铺所有商品
            $goodsCount = Db::name('goods')
                ->where('shop_id', $shop_id)
                ->where('del', 0)
                ->update([
                    'status' => 0, // 下架状态
                    'update_time' => time()
                ]);
            $result['messages'][] = "已下架商家所有商品，共 {$goodsCount} 个";

            // 2. 将user表里面的shop_id清空
            $userCount = Db::name('user')
                ->where('shop_id', $shop_id)
                ->update([
                    'shop_id' => 0,
                    'update_time' => time()
                ]);
            $result['messages'][] = "已清空用户关联，共 {$userCount} 个用户";

            // 3. 删除这个店铺所有的账号
            $adminCount = Db::name('shop_admin')
                ->where('shop_id', $shop_id)
                ->update([
                    'del' => 1,
                    'update_time' => time()
                ]);
            $result['messages'][] = "已删除商家账号，共 {$adminCount} 个账号";

            // 4. 清除商户所配置的广告位
            $adCount = Db::name('ad')
                ->where('shop_id', $shop_id)
                ->update([
                    'status' => 0, // 关闭状态
                    'update_time' => time()
                ]);
            $result['messages'][] = "已清除商家广告位，共 {$adCount} 个广告位";

            // 5. 处理保证金退款
            $deposit = Db::name('shop_deposit')
                ->where('shop_id', $shop_id)
                ->where('pay_status', 1) // 已支付状态
                ->find();

            if ($deposit) {
                $paymentDate = strtotime($deposit['payment_date']);
                $now = time();
                $daysDiff = floor(($now - $paymentDate) / (60 * 60 * 24));

                if ($daysDiff <= 365) {
                    // 支付时间不超过365天，可以微信退款
                    $result['deposit_refund_type'] = 'wechat';
                    $result['messages'][] = "保证金支付时间不超过365天，可以通过微信退款";
                } else {
                    // 支付时间超过365天，需要转账退款
                    $result['deposit_refund_type'] = 'transfer';
                    $result['messages'][] = "保证金支付时间超过365天，需要通过转账退款";
                }

                $result['deposit_amount'] = $deposit['deposit_amount'];
                $result['deposit_id'] = $deposit['id'];
            } else {
                $result['messages'][] = "未找到已支付的保证金记录";
            }

            // 6. 更新商家状态为已注销
            Db::name('shop')
                ->where('id', $shop_id)
                ->update([
                    'is_freeze' => 1, // 冻结状态
                    'update_time' => time()
                ]);
            $result['messages'][] = "已将商家状态更新为已注销";

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $result['success'] = false;
            $result['messages'][] = "执行注销流程失败：" . $e->getMessage();
        }

        return $result;
    }
}
