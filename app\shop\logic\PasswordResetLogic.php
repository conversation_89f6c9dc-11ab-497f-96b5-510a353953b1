<?php

namespace app\shop\logic;

use app\common\basics\Logic;
use app\common\model\shop\ShopAdmin;
use app\common\model\Shop;
use app\common\server\SmsServer;
use think\Db;
use think\facade\Cache;

/**
 * 商家密码重置逻辑
 * Class PasswordResetLogic
 * @package app\shop\logic
 */
class PasswordResetLogic extends Logic
{
    /**
     * 发送重置密码验证码
     * @param array $params
     * @return bool|string
     */
    public static function sendResetCode($params)
    {
        try {
            $mobile = $params['mobile'];
            
            // 查找商家信息（通过shop表的mobile字段）
            $shop = Shop::where(['mobile' => $mobile, 'del' => 0])->findOrEmpty();
            if ($shop->isEmpty()) {
                return '该手机号未注册商家账号';
            }
            
            // 查找对应的管理员账号
            $admin = ShopAdmin::where(['shop_id' => $shop['id'], 'del' => 0, 'root' => 1])->findOrEmpty();
            if ($admin->isEmpty()) {
                return '未找到对应的管理员账号';
            }
            
            // 检查发送频率限制（1分钟内只能发送一次）
            $cacheKey = 'password_reset_' . $mobile;
            if (Cache::get($cacheKey)) {
                return '验证码发送过于频繁，请稍后再试';
            }
            
            // 生成6位数字验证码
            $code = str_pad(mt_rand(0, 999999), 6, '0', STR_PAD_LEFT);
            
            // 生成重置令牌
            $token = md5($mobile . $code . time() . mt_rand(1000, 9999));
            
            // 保存到数据库
            $resetData = [
                'admin_id' => $admin['id'],
                'shop_id' => $shop['id'],
                'mobile' => $mobile,
                'code' => $code,
                'token' => $token,
                'expire_time' => time() + 600, // 10分钟有效期
                'used' => 0,
                'create_time' => time(),
                'update_time' => time()
            ];
            
            Db::name('shop_password_reset')->insert($resetData);
            
            // 发送短信验证码
            $smsResult = SmsServer::send($mobile, [
                'code' => $code,
                'time' => '10分钟'
            ], 'RESET_PASSWORD');
            
            if ($smsResult !== true) {
                return '短信发送失败：' . $smsResult;
            }
            
            // 设置缓存，防止频繁发送
            Cache::set($cacheKey, true, 60);
            
            return true;
            
        } catch (\Exception $e) {
            return '发送验证码失败：' . $e->getMessage();
        }
    }
    
    /**
     * 验证重置码
     * @param array $params
     * @return array|string
     */
    public static function verifyResetCode($params)
    {
        try {
            $mobile = $params['mobile'];
            $code = $params['code'];
            
            // 查找有效的重置记录
            $resetRecord = Db::name('shop_password_reset')
                ->where([
                    'mobile' => $mobile,
                    'code' => $code,
                    'used' => 0
                ])
                ->where('expire_time', '>', time())
                ->order('create_time desc')
                ->find();
                
            if (!$resetRecord) {
                return '验证码无效或已过期';
            }
            
            return [
                'token' => $resetRecord['token'],
                'admin_id' => $resetRecord['admin_id']
            ];
            
        } catch (\Exception $e) {
            return '验证失败：' . $e->getMessage();
        }
    }
    
    /**
     * 重置密码
     * @param array $params
     * @return bool|string
     */
    public static function resetPassword($params)
    {
        try {
            $token = $params['token'];
            $newPassword = $params['password'];
            
            // 查找有效的重置记录
            $resetRecord = Db::name('shop_password_reset')
                ->where([
                    'token' => $token,
                    'used' => 0
                ])
                ->where('expire_time', '>', time())
                ->find();
                
            if (!$resetRecord) {
                return '重置令牌无效或已过期';
            }
            
            // 生成新的密码盐和加密密码
            $salt = create_salt();
            $password = create_password($newPassword, $salt);
            
            // 更新管理员密码
            $updateResult = ShopAdmin::where(['id' => $resetRecord['admin_id']])
                ->update([
                    'password' => $password,
                    'salt' => $salt,
                    'update_time' => time()
                ]);
                
            if (!$updateResult) {
                return '密码更新失败';
            }
            
            // 标记重置记录为已使用
            Db::name('shop_password_reset')
                ->where(['id' => $resetRecord['id']])
                ->update([
                    'used' => 1,
                    'update_time' => time()
                ]);
            
            return true;
            
        } catch (\Exception $e) {
            return '重置密码失败：' . $e->getMessage();
        }
    }
}
