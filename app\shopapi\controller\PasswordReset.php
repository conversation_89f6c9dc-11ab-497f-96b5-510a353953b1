<?php

namespace app\shopapi\controller;

use app\common\basics\Controller;
use app\shop\logic\PasswordResetLogic;
use app\shop\validate\PasswordResetValidate;

/**
 * 商家API密码重置控制器
 * Class PasswordReset
 * @package app\shopapi\controller
 */
class PasswordReset extends Controller
{
    /**
     * 发送重置密码验证码
     * @return \think\response\Json
     */
    public function sendCode()
    {
        $params = $this->request->post();
        
        $validate = new PasswordResetValidate();
        if (!$validate->scene('sendCode')->check($params)) {
            return $this->fail($validate->getError());
        }

        $result = PasswordResetLogic::sendResetCode($params);
        if ($result === true) {
            return $this->success('验证码发送成功');
        }
        
        return $this->fail($result);
    }

    /**
     * 验证重置码
     * @return \think\response\Json
     */
    public function verifyCode()
    {
        $params = $this->request->post();
        
        $validate = new PasswordResetValidate();
        if (!$validate->scene('verifyCode')->check($params)) {
            return $this->fail($validate->getError());
        }

        $result = PasswordResetLogic::verifyResetCode($params);
        if (is_array($result)) {
            return $this->success('验证成功', $result);
        }
        
        return $this->fail($result);
    }

    /**
     * 重置密码
     * @return \think\response\Json
     */
    public function resetPassword()
    {
        $params = $this->request->post();
        
        $validate = new PasswordResetValidate();
        if (!$validate->scene('resetPassword')->check($params)) {
            return $this->fail($validate->getError());
        }

        $result = PasswordResetLogic::resetPassword($params);
        if ($result === true) {
            return $this->success('密码重置成功');
        }
        
        return $this->fail($result);
    }
}
