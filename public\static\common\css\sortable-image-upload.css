/**
 * 可排序图片上传组件样式
 */

/* 排序容器 */
.sortable-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 10px;
    min-height: 120px;
    padding: 10px;
    border: 2px dashed #e6e6e6;
    border-radius: 8px;
    background-color: #fafafa;
    transition: all 0.3s ease;
}

.sortable-container:empty {
    display: none;
}

.sortable-container.drag-over {
    border-color: #1890ff;
    background-color: #f0f8ff;
}

/* 图片项容器 */
.sortable-image-item {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: move;
}

.sortable-image-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 图片包装器 */
.image-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
}

/* 上传的图片 */
.uploaded-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* 图片控制层 */
.image-controls {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sortable-image-item:hover .image-controls {
    opacity: 1;
}

/* 图片序号 */
.image-order {
    position: absolute;
    top: 5px;
    left: 5px;
    background: #1890ff;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

/* 图片操作按钮 */
.image-actions {
    position: absolute;
    top: 5px;
    right: 5px;
    display: flex;
    gap: 5px;
}

.image-actions button {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.9);
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.image-actions button:hover {
    background: #fff;
    color: #333;
    transform: scale(1.1);
}

.btn-delete:hover {
    background: #ff4d4f !important;
    color: white !important;
}

.btn-preview:hover {
    background: #1890ff !important;
    color: white !important;
}

/* 拖动手柄 */
.drag-handle {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 24px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: move;
    font-size: 14px;
}

.drag-handle:hover {
    background: rgba(0, 0, 0, 0.8);
}

/* Sortable.js 相关样式 */
.sortable-ghost {
    opacity: 0.5;
    background: #f0f8ff;
    border: 2px dashed #1890ff;
}

.sortable-chosen {
    transform: rotate(5deg);
}

.sortable-drag {
    transform: rotate(5deg);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sortable-image-item {
        width: 100px;
        height: 100px;
    }
    
    .sortable-container {
        gap: 8px;
        padding: 8px;
    }
    
    .image-actions button {
        width: 20px;
        height: 20px;
    }
    
    .image-order {
        width: 18px;
        height: 18px;
        font-size: 11px;
    }
}

/* 兼容原有的上传样式 */
.like-upload-image .upload-image-elem {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    background: #fafafa;
    transition: all 0.3s ease;
}

.like-upload-image .upload-image-elem:hover {
    border-color: #1890ff;
    background: #f0f8ff;
}

.like-upload-image .upload-image-elem a {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 120px;
    color: #666;
    text-decoration: none;
    font-size: 14px;
}

.like-upload-image .upload-image-elem a:hover {
    color: #1890ff;
}

/* 加载状态 */
.sortable-container.loading {
    position: relative;
}

.sortable-container.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态提示 */
.sortable-container.empty::before {
    content: '拖拽图片到此处或点击上传';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #999;
    font-size: 14px;
    pointer-events: none;
}

/* 错误状态 */
.sortable-image-item.error {
    border: 2px solid #ff4d4f;
}

.sortable-image-item.error .image-controls {
    background: rgba(255, 77, 79, 0.8);
}

/* 成功状态 */
.sortable-image-item.success {
    border: 2px solid #52c41a;
}

.sortable-image-item.success::after {
    content: '✓';
    position: absolute;
    top: 5px;
    right: 5px;
    width: 20px;
    height: 20px;
    background: #52c41a;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}
